import { Context, Hono } from "hono";
import dotenv from "dotenv";
import path from "path";

// Load environment from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// LangGraph-based Procurement Agent with Perplexity Sonar Pro
interface PerplexityResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class PerplexityAgent {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async query(prompt: string, instructions: string): Promise<string> {
    try {
      const response = await fetch('https://api.perplexity.ai/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'sonar-pro',
          messages: [
            { role: 'system', content: instructions },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 2000,
        }),
      });

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.statusText}`);
      }

      const data: PerplexityResponse = await response.json();
      return data.choices[0]?.message?.content || 'No response generated';
    } catch (error) {
      console.error('Perplexity API error:', error);
      return `Error: ${error.message}`;
    }
  }
}


// Basic API endpoint
export const registerLangbaseEndpoint = (app: Hono) => {
  // Existing route used by current frontend
  app.post("/api/langbase", async (c: Context) => {
    const request = new Request(c.req.url, {
      method: c.req.method,
      headers: {
        "Content-Type": c.req.header("Content-Type") || "application/json",
        Authorization: c.req.header("Authorization") || "",
      },
      body: JSON.stringify(await c.req.json()),
    });
    return handleAgentRequest(request);
  });

  // New alias route to match "/api/agent" (Express-style sample)
  app.post("/api/agent", async (c: Context) => {
    const request = new Request(c.req.url, {
      method: c.req.method,
      headers: {
        "Content-Type": c.req.header("Content-Type") || "application/json",
      },
      body: JSON.stringify(await c.req.json()),
    });
    return handleAgentRequest(request);
  });
};

// Server-side only: Do NOT include it in any client-side code, that ends up in the browsers.

function isDegraded(data: any): boolean {
  try {
    const s = JSON.stringify(data || {}).toLowerCase();
    return (
      s.includes('limited analysis available') ||
      s.includes('please check your api keys') ||
      (typeof data === 'object' && data && (data.status === 'error' || data.message === 'Procurement analysis completed with limited data'))
    );
  } catch {
    return false;
  }
}

async function runHosted(input: string) {
  const apiKey = process.env.LANGBASE_API_KEY;
  if (!apiKey) {
    return { ok: false, status: 500, data: { error: 'LANGBASE_API_KEY is not set. Add it to app/.env.' } };
  }
  const apiUrl = process.env.LANGBASE_API_URL || 'https://api.langbase.com/aymnsh174883/perplex-procure-agent-4b33';
  try {
    const resp = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${apiKey}` },
      body: JSON.stringify({ input })
    });
    const data = await resp.json().catch(() => ({}));
    return { ok: resp.ok, status: resp.status, data };
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    return { ok: false, status: 500, data: { error: message } };
  }
}

async function runLocalWorkflow(input: string) {
  const perplexityAgent = new PerplexityAgent(process.env.PERPLEXITY_API_KEY!);

  try {
    console.log('Step 1: Analyzing query...');
    const queryAnalysis = await perplexityAgent.query(
      input,
      `You are a Procurement AI Agent. Analyze the user query and determine:
      1. Type of procurement request (supplier search, RFQ generation, invoice processing, etc.)
      2. Key requirements and constraints
      3. Budget considerations
      4. Regional preferences (especially for Mangaluru, India)
      5. Compliance requirements

      Respond with a structured analysis including the procurement type and key parameters.`
    );

    console.log('Step 2: Retrieving procurement data...');
    // For now, use fallback data - can be enhanced with actual memory retrieval later
    const relevantMemories = [{
      text: "Fallback procurement policies: Standard approval workflows, ESG compliance required, regional supplier preferences for Mangaluru market."
    }];

    console.log('Step 3: Conducting market research...');
    const marketResearch = await perplexityAgent.query(
      `Research market data for: ${input}`,
      `Search for current market information related to the procurement request. Include:
      1. Current market prices and trends
      2. Available suppliers (especially in Mangaluru region if relevant)
      3. Compliance and certification requirements
      4. Sustainability and ESG factors
      5. Recent industry developments

      Provide comprehensive market intelligence for informed procurement decisions.`
    );

    console.log('Step 4: Generating recommendations...');
    const context = `Analysis: ${queryAnalysis}\n\nKnowledge: ${relevantMemories.map((m:any)=>m.text).join('\n')}\n\nMarket: ${marketResearch}`;
    const recommendations = await perplexityAgent.query(
      `Generate procurement recommendations from: ${context}`,
      `You are an expert Procurement AI Agent. Based on the analysis and research, provide:

      1. **Supplier Recommendations**: Top 3-5 suppliers with rationale
      2. **Cost Analysis**: Budget estimates and cost-saving opportunities
      3. **Risk Assessment**: Potential risks and mitigation strategies
      4. **Compliance Check**: Regulatory and ESG compliance status
      5. **Timeline**: Recommended procurement timeline
      6. **Next Steps**: Specific actions to take (RFQ, RFP, direct purchase, etc.)
      7. **Regional Considerations**: Special considerations for Mangaluru/India market
      8. **Approval Workflow**: Required approvals based on budget and policies

      Format the response as a comprehensive procurement recommendation report.`
    );

    console.log('Step 5: Generating documents...');
    const documents = await perplexityAgent.query(
      `Generate procurement documents for: ${input}\n\nBased on recommendations: ${recommendations}`,
      `Based on the procurement recommendations, generate appropriate procurement documents:

      1. If supplier search: Create RFQ (Request for Quotation) template
      2. If complex procurement: Create RFP (Request for Proposal) outline
      3. If direct purchase: Create PO (Purchase Order) draft
      4. Include all necessary terms, conditions, and specifications
      5. Ensure compliance with company policies and regional regulations
      6. Add ESG and sustainability requirements where applicable

      Format as ready-to-use procurement documents.`
    );

    console.log('Step 6: Creating action plan...');
    const actionPlan = await perplexityAgent.query(
      `Create action plan for procurement request: ${input}

      Recommendations: ${recommendations}
      Documents: ${documents}`,
      `Create a comprehensive procurement action plan that includes:

      1. **Executive Summary**: Key findings and recommendations
      2. **Immediate Actions**: What to do next (within 24-48 hours)
      3. **Short-term Actions**: Steps for the next 1-2 weeks
      4. **Long-term Strategy**: Ongoing procurement optimization
      5. **Budget Impact**: Financial implications and savings opportunities
      6. **Risk Mitigation**: How to address identified risks
      7. **Compliance Checklist**: Ensure all regulatory requirements are met
      8. **Performance Metrics**: KPIs to track procurement success
      9. **Stakeholder Communication**: Who needs to be informed/involved
      10. **Regional Adaptations**: Specific considerations for local market

      Make it actionable and specific to the user's procurement needs.`
    );

    const result = {
      status: 'success',
      source: 'local-langgraph',
      query_analysis: queryAnalysis,
      market_intelligence: marketResearch,
      recommendations,
      documents,
      action_plan: actionPlan,
      timestamp: new Date().toISOString(),
      region: "Mangaluru, India (with global considerations)"
    };

    return { ok: true, status: 200, data: result };
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e);
    return { ok: false, status: 500, data: { status: 'error', message } };
  }
}

async function handleAgentRequest(request: Request) {
  try {
    const { input } = await request.json();

    if (!input || typeof input !== 'string') {
      return new Response(JSON.stringify({ error: "Input is required and must be a string" }), { status: 400, headers: { "Content-Type": "application/json" } });
    }

    const mode = (process.env.AGENT_MODE || 'hosted').toLowerCase();

    if (mode === 'local') {
      const local = await runLocalWorkflow(input);
      return new Response(JSON.stringify(local.data), { status: local.status, headers: { 'Content-Type': 'application/json' } });
    }

    // Hosted-first, fallback to local on failure or degraded output
    const hosted = await runHosted(input);
    if (!hosted.ok || isDegraded(hosted.data)) {
      const local = await runLocalWorkflow(input);
      const payload = { fallback_from: 'hosted', hosted_status: hosted.status, hosted_preview: hosted.data, ...local.data };
      return new Response(JSON.stringify(payload), { status: local.status, headers: { 'Content-Type': 'application/json' } });
    }

    // Phase 2: try to keep structured result shape if hosted provides it
    return new Response(JSON.stringify({ source: 'hosted', ...hosted.data }), { status: hosted.status, headers: { 'Content-Type': 'application/json' } });

  } catch (error) {
    console.error('Error in handleAgentRequest:', error);
    const message = error instanceof Error ? error.message : String(error);
    return new Response(JSON.stringify({ error: message || 'Internal server error' }), { status: 500, headers: { "Content-Type": "application/json" } });
  }
}