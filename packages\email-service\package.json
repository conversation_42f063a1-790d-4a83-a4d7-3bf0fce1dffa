{"name": "@procuregpt/email-service", "version": "1.0.0", "description": "Email service for ProcureGPT", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"typescript": "^5.3.3", "vitest": "^1.2.0", "@types/node": "^20.11.0"}, "dependencies": {"@procuregpt/types": "workspace:*", "@procuregpt/config": "workspace:*", "nodemailer": "^6.9.8", "@types/nodemailer": "^6.4.14", "zod": "^3.25.67"}}