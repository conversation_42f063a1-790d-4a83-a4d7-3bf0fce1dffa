{"name": "@procuregpt/web", "version": "1.0.0", "description": "ProcureGPT Web Application", "type": "module", "scripts": {"build": "vite build", "dev": "vite dev --port 3100", "preview": "vite preview", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@procuregpt/shared": "workspace:*", "@procuregpt/types": "workspace:*", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.344.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-markdown": "^9.0.1", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tw-animate-css": "^1.3.7", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0-alpha.4", "@types/node": "^20.11.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "tailwindcss": "^4.0.0-alpha.4", "typescript": "^5.3.3", "vite": "^5.1.0", "vitest": "^1.2.0"}}