import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { appConfig } from '@procuregpt/config';
import { diContainer, TOKENS } from '@procuregpt/shared';
import { errorHandler } from './middleware/error-handler';
import { authMiddleware } from './middleware/auth';
import { workflowRoutes } from './routes/workflow';
import { procurementRoutes } from './routes/procurement';
import { supplierRoutes } from './routes/supplier';
import { rfqRoutes } from './routes/rfq';
import { authRoutes } from './routes/auth';
import { healthRoutes } from './routes/health';
import { registerLangbaseEndpoint } from '../langbase';

export async function createApp() {
  const app = new Hono();

  // Global middleware
  app.use('*', logger());
  app.use('*', prettyJSON());
  app.use('*', secureHeaders());
  app.use('*', cors({
    origin: appConfig.corsOrigins,
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  }));

  // Health check (no auth required)
  app.route('/health', healthRoutes);

  // Authentication routes (no auth required)
  app.route('/auth', authRoutes);

  // Register langbase endpoint (no auth required for now)
  registerLangbaseEndpoint(app);

  // API routes (auth required)
  app.use('/api/*', authMiddleware);
  app.route('/api/workflows', workflowRoutes);
  app.route('/api/procurement', procurementRoutes);
  app.route('/api/suppliers', supplierRoutes);
  app.route('/api/rfq', rfqRoutes);

  // API documentation
  app.get('/docs', (c) => {
    return c.html(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>ProcureGPT API Documentation</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            h1 { color: #333; }
            .endpoint { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; background: #f5f5f5; }
            .method { font-weight: bold; color: #007acc; }
          </style>
        </head>
        <body>
          <h1>🚀 ProcureGPT API Documentation</h1>
          <p>Welcome to the ProcureGPT API. This is a production-grade procurement automation platform.</p>
          
          <div class="endpoint">
            <div class="method">GET /health</div>
            <p>Health check endpoint</p>
          </div>
          
          <div class="endpoint">
            <div class="method">POST /auth/login</div>
            <p>Authenticate user and get JWT token</p>
          </div>
          
          <div class="endpoint">
            <div class="method">POST /api/workflows/execute</div>
            <p>Execute a workflow (returns job ID for async processing)</p>
          </div>
          
          <div class="endpoint">
            <div class="method">GET /api/workflows/{id}/status</div>
            <p>Get workflow execution status</p>
          </div>
          
          <div class="endpoint">
            <div class="method">GET /api/procurement/requests</div>
            <p>List procurement requests</p>
          </div>
          
          <div class="endpoint">
            <div class="method">POST /api/rfq</div>
            <p>Create new RFQ</p>
          </div>
          
          <div class="endpoint">
            <div class="method">GET /api/suppliers</div>
            <p>List suppliers with search and filtering</p>
          </div>
        </body>
      </html>
    `);
  });

  // 404 handler
  app.notFound((c) => {
    return c.json({
      success: false,
      error: 'Not Found',
      message: 'The requested resource was not found',
      timestamp: new Date().toISOString(),
    }, 404);
  });

  // Global error handler
  app.onError(errorHandler);

  return app;
}
